import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwindcss/merge";
import { Task, TaskFilter, TaskStats, Priority } from '@/types';
import { isToday, isTomorrow, isPast, startOfDay } from 'date-fns';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Priority colors and labels
export const priorityConfig = {
  low: {
    label: 'Low',
    color: 'bg-green-100 text-green-800 border-green-200',
    dotColor: 'bg-green-500',
  },
  medium: {
    label: 'Medium',
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    dotColor: 'bg-yellow-500',
  },
  high: {
    label: 'High',
    color: 'bg-red-100 text-red-800 border-red-200',
    dotColor: 'bg-red-500',
  },
} as const;

// Default category colors
export const categoryColors = [
  '#3B82F6', // blue
  '#10B981', // emerald
  '#F59E0B', // amber
  '#EF4444', // red
  '#8B5CF6', // violet
  '#06B6D4', // cyan
  '#84CC16', // lime
  '#F97316', // orange
  '#EC4899', // pink
  '#6B7280', // gray
];

// Filter tasks based on criteria
export function filterTasks(tasks: Task[], filter: TaskFilter): Task[] {
  return tasks.filter(task => {
    // Status filter
    if (filter.status && task.status !== filter.status) {
      return false;
    }

    // Priority filter
    if (filter.priority && task.priority !== filter.priority) {
      return false;
    }

    // Category filter
    if (filter.categoryId && task.categoryId !== filter.categoryId) {
      return false;
    }

    // Search filter
    if (filter.search) {
      const searchLower = filter.search.toLowerCase();
      const titleMatch = task.title.toLowerCase().includes(searchLower);
      const descriptionMatch = task.description?.toLowerCase().includes(searchLower);
      if (!titleMatch && !descriptionMatch) {
        return false;
      }
    }

    // Due date range filter
    if (filter.dueDateRange) {
      if (!task.dueDate) return false;
      
      const taskDate = startOfDay(task.dueDate);
      
      if (filter.dueDateRange.start) {
        const startDate = startOfDay(filter.dueDateRange.start);
        if (taskDate < startDate) return false;
      }
      
      if (filter.dueDateRange.end) {
        const endDate = startOfDay(filter.dueDateRange.end);
        if (taskDate > endDate) return false;
      }
    }

    return true;
  });
}

// Calculate task statistics
export function calculateTaskStats(tasks: Task[]): TaskStats {
  const total = tasks.length;
  const completed = tasks.filter(task => task.status === 'completed').length;
  const pending = tasks.filter(task => task.status === 'pending').length;
  
  const now = new Date();
  const overdue = tasks.filter(task => 
    task.status === 'pending' && 
    task.dueDate && 
    isPast(task.dueDate) && 
    !isToday(task.dueDate)
  ).length;
  
  const dueToday = tasks.filter(task => 
    task.status === 'pending' && 
    task.dueDate && 
    isToday(task.dueDate)
  ).length;
  
  const dueTomorrow = tasks.filter(task => 
    task.status === 'pending' && 
    task.dueDate && 
    isTomorrow(task.dueDate)
  ).length;

  return {
    total,
    completed,
    pending,
    overdue,
    dueToday,
    dueTomorrow,
  };
}

// Sort tasks by priority, due date, and creation date
export function sortTasks(tasks: Task[]): Task[] {
  const priorityOrder: Record<Priority, number> = {
    high: 3,
    medium: 2,
    low: 1,
  };

  return [...tasks].sort((a, b) => {
    // First, sort by completion status (pending first)
    if (a.status !== b.status) {
      return a.status === 'pending' ? -1 : 1;
    }

    // For pending tasks, sort by priority
    if (a.status === 'pending') {
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;

      // Then by due date (earliest first, no due date last)
      if (a.dueDate && b.dueDate) {
        return a.dueDate.getTime() - b.dueDate.getTime();
      }
      if (a.dueDate && !b.dueDate) return -1;
      if (!a.dueDate && b.dueDate) return 1;
    }

    // Finally, sort by creation date (newest first)
    return b.createdAt.getTime() - a.createdAt.getTime();
  });
}

// Check if a task is overdue
export function isTaskOverdue(task: Task): boolean {
  return task.status === 'pending' && 
         task.dueDate !== undefined && 
         isPast(task.dueDate) && 
         !isToday(task.dueDate);
}

// Check if a task is due today
export function isTaskDueToday(task: Task): boolean {
  return task.status === 'pending' && 
         task.dueDate !== undefined && 
         isToday(task.dueDate);
}

// Format relative date
export function formatRelativeDate(date: Date): string {
  if (isToday(date)) return 'Today';
  if (isTomorrow(date)) return 'Tomorrow';
  if (isPast(date)) return 'Overdue';
  
  const diffTime = date.getTime() - new Date().getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) return 'Tomorrow';
  if (diffDays < 7) return `In ${diffDays} days`;
  if (diffDays < 30) return `In ${Math.ceil(diffDays / 7)} weeks`;
  
  return date.toLocaleDateString();
}
