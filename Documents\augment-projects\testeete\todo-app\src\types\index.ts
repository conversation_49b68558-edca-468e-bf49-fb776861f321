export type Priority = 'low' | 'medium' | 'high';

export type TaskStatus = 'pending' | 'completed';

export interface Category {
  id: string;
  name: string;
  color: string;
  createdAt: Date;
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: Priority;
  categoryId?: string;
  dueDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

export interface TaskFilter {
  status?: TaskStatus;
  priority?: Priority;
  categoryId?: string;
  search?: string;
  dueDateRange?: {
    start?: Date;
    end?: Date;
  };
}

export interface TaskStats {
  total: number;
  completed: number;
  pending: number;
  overdue: number;
  dueToday: number;
  dueTomorrow: number;
}

export interface AppState {
  tasks: Task[];
  categories: Category[];
  filter: TaskFilter;
  isLoading: boolean;
  error?: string;
}
