import { Task, Category } from '@/types';

const STORAGE_KEYS = {
  TASKS: 'todo-app-tasks',
  CATEGORIES: 'todo-app-categories',
} as const;

// Helper function to safely parse JSON from localStorage
function safeJsonParse<T>(value: string | null, fallback: T): T {
  if (!value) return fallback;
  
  try {
    const parsed = JSON.parse(value);
    // Convert date strings back to Date objects
    if (Array.isArray(parsed)) {
      return parsed.map(item => ({
        ...item,
        createdAt: new Date(item.createdAt),
        updatedAt: item.updatedAt ? new Date(item.updatedAt) : undefined,
        completedAt: item.completedAt ? new Date(item.completedAt) : undefined,
        dueDate: item.dueDate ? new Date(item.dueDate) : undefined,
      })) as T;
    }
    return parsed;
  } catch (error) {
    console.error('Error parsing JSON from localStorage:', error);
    return fallback;
  }
}

// Helper function to safely stringify and store in localStorage
function safeJsonStringify<T>(key: string, value: T): void {
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.error('Error storing data in localStorage:', error);
  }
}

export const storage = {
  // Task operations
  getTasks(): Task[] {
    if (typeof window === 'undefined') return [];
    return safeJsonParse(localStorage.getItem(STORAGE_KEYS.TASKS), []);
  },

  saveTasks(tasks: Task[]): void {
    if (typeof window === 'undefined') return;
    safeJsonStringify(STORAGE_KEYS.TASKS, tasks);
  },

  addTask(task: Task): void {
    const tasks = this.getTasks();
    tasks.push(task);
    this.saveTasks(tasks);
  },

  updateTask(taskId: string, updates: Partial<Task>): void {
    const tasks = this.getTasks();
    const index = tasks.findIndex(task => task.id === taskId);
    if (index !== -1) {
      tasks[index] = { ...tasks[index], ...updates, updatedAt: new Date() };
      this.saveTasks(tasks);
    }
  },

  deleteTask(taskId: string): void {
    const tasks = this.getTasks();
    const filteredTasks = tasks.filter(task => task.id !== taskId);
    this.saveTasks(filteredTasks);
  },

  // Category operations
  getCategories(): Category[] {
    if (typeof window === 'undefined') return [];
    return safeJsonParse(localStorage.getItem(STORAGE_KEYS.CATEGORIES), []);
  },

  saveCategories(categories: Category[]): void {
    if (typeof window === 'undefined') return;
    safeJsonStringify(STORAGE_KEYS.CATEGORIES, categories);
  },

  addCategory(category: Category): void {
    const categories = this.getCategories();
    categories.push(category);
    this.saveCategories(categories);
  },

  updateCategory(categoryId: string, updates: Partial<Category>): void {
    const categories = this.getCategories();
    const index = categories.findIndex(cat => cat.id === categoryId);
    if (index !== -1) {
      categories[index] = { ...categories[index], ...updates };
      this.saveCategories(categories);
    }
  },

  deleteCategory(categoryId: string): void {
    const categories = this.getCategories();
    const filteredCategories = categories.filter(cat => cat.id !== categoryId);
    this.saveCategories(filteredCategories);
    
    // Also remove category from tasks
    const tasks = this.getTasks();
    const updatedTasks = tasks.map(task => 
      task.categoryId === categoryId 
        ? { ...task, categoryId: undefined, updatedAt: new Date() }
        : task
    );
    this.saveTasks(updatedTasks);
  },

  // Clear all data
  clearAll(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(STORAGE_KEYS.TASKS);
    localStorage.removeItem(STORAGE_KEYS.CATEGORIES);
  },
};
